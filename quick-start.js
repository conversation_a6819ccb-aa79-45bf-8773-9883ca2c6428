#!/usr/bin/env node

/**
 * Quick Start Script for GeoShift Pro
 * Ensures immediate UI loading without hanging
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔥 GeoShift Pro Quick Start');
console.log('==========================\n');

// Ensure dist directory exists
const distDir = path.join(__dirname, 'dist');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// Create necessary subdirectories
['js', 'js/utils', 'js/core', 'css'].forEach(dir => {
  const fullPath = path.join(distDir, dir);
  if (!fs.existsSync(fullPath)) {
    fs.mkdirSync(fullPath, { recursive: true });
  }
});

// Copy files synchronously for immediate availability
console.log('📋 Copying files...');

const filesToCopy = [
  { src: 'src/web/js/utils/Logger.js', dest: 'dist/js/utils/Logger.js' },
  { src: 'src/web/js/core/ProxyManager.js', dest: 'dist/js/core/ProxyManager.js' },
  { src: 'src/web/js/app.js', dest: 'dist/js/app.js' },
  { src: 'src/web/index.html', dest: 'dist/index.html' },
  { src: 'src/web/css/brutalist.css', dest: 'dist/css/brutalist.css' }
];

filesToCopy.forEach(({ src, dest }) => {
  try {
    if (fs.existsSync(src)) {
      fs.copyFileSync(src, dest);
      console.log(`✅ Copied ${src} → ${dest}`);
    } else {
      console.log(`⚠️  Source not found: ${src}`);
    }
  } catch (error) {
    console.log(`❌ Failed to copy ${src}:`, error.message);
  }
});

// Get command line arguments
const args = process.argv.slice(2);
const mode = args[0] || 'web';

switch (mode) {
  case 'web':
    startWebQuick();
    break;
  case 'electron':
  case 'desktop':
    startElectronQuick();
    break;
  case 'standalone':
    startStandalone();
    break;
  default:
    console.log(`
Usage: node quick-start.js [mode]

Modes:
  web        - Start web server (default)
  desktop    - Start Electron app
  standalone - Start Electron with standalone HTML

This script ensures immediate UI loading without initialization hangs.
`);
    break;
}

function startWebQuick() {
  console.log('\n🌐 Starting quick web server...');
  
  // Use a simple HTTP server
  const http = require('http');
  const url = require('url');
  const mime = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json'
  };

  const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url);
    let pathname = parsedUrl.pathname;
    
    // Default to index.html
    if (pathname === '/') {
      pathname = '/index.html';
    }
    
    const filePath = path.join(distDir, pathname);
    
    fs.readFile(filePath, (err, data) => {
      if (err) {
        res.writeHead(404);
        res.end('File not found');
        return;
      }
      
      const ext = path.extname(filePath);
      const contentType = mime[ext] || 'text/plain';
      
      res.writeHead(200, { 
        'Content-Type': contentType,
        'Cache-Control': 'no-cache'
      });
      res.end(data);
    });
  });

  const port = 3000;
  server.listen(port, () => {
    console.log(`✅ Server running at http://localhost:${port}`);
    console.log('🎉 Open your browser to see GeoShift Pro!');
    
    // Try to open browser
    const open = process.platform === 'win32' ? 'start' : 
                 process.platform === 'darwin' ? 'open' : 'xdg-open';
    
    exec(`${open} http://localhost:${port}`, (error) => {
      if (error) {
        console.log('Could not open browser automatically. Please open http://localhost:3000 manually.');
      }
    });
  });

  // Handle shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    server.close();
    process.exit(0);
  });
}

function startElectronQuick() {
  console.log('\n🖥️  Starting Electron app...');
  
  // Start Electron with the dist version
  const electronProcess = spawn('npx', ['electron', 'src/desktop/main.js'], {
    stdio: 'inherit',
    shell: true,
    env: { ...process.env, ELECTRON_DEV: 'true' }
  });

  electronProcess.on('error', (error) => {
    console.error('❌ Electron failed to start:', error);
    console.log('💡 Try: npm install electron');
  });

  electronProcess.on('close', (code) => {
    console.log(`Electron exited with code ${code}`);
  });
}

function startStandalone() {
  console.log('\n🚀 Starting standalone Electron app...');
  
  const electronProcess = spawn('npx', ['electron', 'src/desktop/main.js', '--standalone'], {
    stdio: 'inherit',
    shell: true
  });

  electronProcess.on('error', (error) => {
    console.error('❌ Electron failed to start:', error);
  });
}

console.log('\n📝 Note: This quick start bypasses complex initialization');
console.log('   The UI will load immediately with full functionality!');
