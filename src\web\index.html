<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GeoShift Pro - Neo-Brutalist VPN/Proxy Tool</title>
    <meta name="description" content="A Neo-Brutalist VPN/Proxy bypass tool for local use with bold design and powerful functionality">
    <meta name="keywords" content="vpn, proxy, bypass, neo-brutalism, privacy, security, networking">
    <meta name="author" content="HectorTa1989">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="./assets/favicon.png">
    
    <!-- Inline Neo-Brutalist Styles for immediate loading -->
    <style>
        /* Critical Neo-Brutalist styles inline for immediate rendering */
        * { box-sizing: border-box; margin: 0; padding: 0; }

        body {
            font-family: 'Courier New', 'Monaco', 'Menlo', 'Consolas', monospace;
            font-size: 14px;
            line-height: 1.4;
            color: #000000;
            background-color: #FFFFFF;
        }

        h1, h2, h3 {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 16px;
        }

        h1 { font-size: 32px; color: #FF0000; text-shadow: 4px 4px 0 #000000; margin-left: 8px; }
        h2 { font-size: 24px; color: #0000FF; margin-right: 5px; }
        h3 { font-size: 20px; color: #00FF00; margin-left: 10px; }

        .btn {
            display: inline-block;
            padding: 8px 16px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
            border: 4px solid #000000;
            background-color: #FFFFFF;
            color: #000000;
            cursor: pointer;
            box-shadow: 4px 4px 0 #000000;
            margin: 4px;
        }

        .btn:hover { transform: translate(2px, 2px); box-shadow: 2px 2px 0 #000000; }
        .btn:active { transform: translate(4px, 4px); box-shadow: none; }
        .btn:disabled { background-color: #CCCCCC; color: #333333; cursor: not-allowed; }

        .btn-primary { background-color: #FF0000; color: #FFFFFF; margin-left: 5px; }
        .btn-secondary { background-color: #0000FF; color: #FFFFFF; margin-right: 8px; }
        .btn-success { background-color: #00FF00; color: #000000; margin-left: 10px; }
        .btn-warning { background-color: #FFFF00; color: #000000; }
        .btn-danger { background-color: #FF00FF; color: #FFFFFF; }

        .card {
            border: 4px solid #000000;
            background-color: #FFFFFF;
            box-shadow: 4px 4px 0 #000000;
            margin: 16px;
            padding: 24px;
        }

        .card-offset-left { margin-left: 40px; }
        .card-offset-right { margin-right: 40px; }
        .card-primary { background-color: #FF0000; color: #FFFFFF; }
        .card-secondary { background-color: #0000FF; color: #FFFFFF; }

        .status {
            display: inline-block;
            padding: 4px 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            border: 3px solid #000000;
            margin: 4px;
        }

        .status-online { background-color: #00FF00; color: #000000; }
        .status-offline { background-color: #FF0000; color: #FFFFFF; }
        .status-connecting { background-color: #FFFF00; color: #000000; animation: blink 1s infinite; }

        .alert {
            padding: 16px;
            border: 4px solid #000000;
            margin: 16px 0;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            text-transform: uppercase;
            box-shadow: 4px 4px 0 #000000;
        }

        .alert-success { background-color: #00FF00; color: #000000; }
        .alert-error { background-color: #FF0000; color: #FFFFFF; }
        .alert-warning { background-color: #FFFF00; color: #000000; }
        .alert-info { background-color: #00FFFF; color: #000000; }

        .container { max-width: 1200px; margin: 0 auto; padding: 16px; }
        .row { display: flex; flex-wrap: wrap; margin: -8px; }
        .col { flex: 1; padding: 8px; }
        .col-offset { margin-left: 8px; }

        .text-center { text-align: center; }
        .text-uppercase { text-transform: uppercase; }
        .text-bold { font-weight: bold; }
        .text-primary { color: #FF0000; }

        @keyframes blink { 0%, 50% { opacity: 1; } 51%, 100% { opacity: 0.3; } }

        /* Loading screen styles */
        .loading-screen {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: linear-gradient(45deg, #FF0000, #00FF00, #0000FF);
            background-size: 400% 400%;
            animation: gradientShift 3s ease infinite;
            display: flex; justify-content: center; align-items: center; z-index: 9999;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .loading-container {
            text-align: center; background-color: #FFFFFF;
            border: 4px solid #000000; box-shadow: 8px 8px 0 #000000;
            padding: 40px; max-width: 400px; width: 90%;
        }

        .loading-logo {
            font-size: 32px; font-weight: bold; color: #FF0000;
            text-shadow: 3px 3px 0 #000000; margin-bottom: 10px;
            text-transform: uppercase; letter-spacing: 2px;
        }

        .loading-subtitle {
            font-size: 12px; color: #0000FF; text-transform: uppercase;
            margin-bottom: 30px; font-weight: bold;
        }

        .loading-text {
            font-size: 10px; color: #000000; text-transform: uppercase;
            font-weight: bold; animation: blink 1s infinite;
        }

        .app-container { min-height: 100vh; background-color: #FFFFFF; }
        .main-content { min-height: calc(100vh - 80px); }

        /* Modal styles */
        .modal {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(0, 0, 0, 0.8); display: none;
            justify-content: center; align-items: center; z-index: 1000;
        }

        .modal-content {
            background-color: #FFFFFF; border: 4px solid #000000;
            box-shadow: 8px 8px 0 #000000; max-width: 600px; width: 90%;
            max-height: 80vh; overflow-y: auto;
        }

        .modal-header {
            background-color: #000000; color: #FFFFFF; padding: 16px;
            display: flex; justify-content: space-between; align-items: center;
        }

        .modal-body { padding: 24px; }

        @media (max-width: 768px) {
            .container { padding: 8px; }
            .row { flex-direction: column; }
            .btn { display: block; width: 100%; margin: 4px 0; }
            h1 { font-size: 24px; }
            .card { margin: 8px; padding: 16px; }
        }
    </style>
    
    <!-- Security headers -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' https://api.ipify.org https://httpbin.org https://ipapi.co https://ip-api.com https://ipinfo.io https://******* https://checkip.amazonaws.com https://icanhazip.com;">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-container">
            <div class="loading-logo">GEOSHIFT PRO</div>
            <div class="loading-subtitle">Neo-Brutalist VPN/Proxy Tool</div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <div class="loading-text">INITIALIZING...</div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="app" class="app-container" style="display: none;">
        <!-- Navigation Header -->
        <nav class="nav-header">
            <div class="container">
                <div class="nav-brand">
                    <h1 class="nav-logo">GEOSHIFT PRO</h1>
                    <span class="nav-tagline">BRUTALIST PROXY TOOL</span>
                </div>
                <div class="nav-controls">
                    <button id="theme-toggle" class="btn btn-sm">THEME</button>
                    <button id="fullscreen-toggle" class="btn btn-sm">FULLSCREEN</button>
                    <button id="help-toggle" class="btn btn-sm">HELP</button>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main id="main-content" class="main-content">
            <!-- Dashboard will be rendered here -->
        </main>

        <!-- Help Modal -->
        <div id="help-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>HELP & INSTRUCTIONS</h2>
                    <button id="close-help" class="btn btn-danger">×</button>
                </div>
                <div class="modal-body">
                    <div class="help-section">
                        <h3>GETTING STARTED</h3>
                        <ul>
                            <li>Click "CONNECT" to start using a proxy</li>
                            <li>Use "ROTATE PROXY" to switch servers</li>
                            <li>Run "TEST CONNECTION" to check effectiveness</li>
                            <li>"REFRESH PROXIES" loads new proxy servers</li>
                        </ul>
                    </div>
                    <div class="help-section">
                        <h3>KEYBOARD SHORTCUTS</h3>
                        <ul>
                            <li><strong>CTRL+N:</strong> New Connection</li>
                            <li><strong>CTRL+D:</strong> Disconnect</li>
                            <li><strong>CTRL+R:</strong> Rotate Proxy</li>
                            <li><strong>CTRL+T:</strong> Test Connection</li>
                            <li><strong>F11:</strong> Toggle Fullscreen</li>
                        </ul>
                    </div>
                    <div class="help-section">
                        <h3>STATUS INDICATORS</h3>
                        <ul>
                            <li><span class="status status-online">ONLINE</span> - Connected and working</li>
                            <li><span class="status status-offline">OFFLINE</span> - Not connected</li>
                            <li><span class="status status-connecting">CONNECTING</span> - In progress</li>
                            <li><span class="status status-error">ERROR</span> - Connection failed</li>
                        </ul>
                    </div>
                    <div class="help-section">
                        <h3>ABOUT NEO-BRUTALISM</h3>
                        <p>This interface uses Neo-Brutalist design principles:</p>
                        <ul>
                            <li>Bold, high-contrast colors</li>
                            <li>Thick black borders on all elements</li>
                            <li>Harsh drop shadows without blur</li>
                            <li>Monospace typography throughout</li>
                            <li>Intentionally "undesigned" aesthetic</li>
                            <li>Asymmetrical layouts for visual interest</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Modal -->
        <div id="error-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>ERROR</h2>
                    <button id="close-error" class="btn btn-danger">×</button>
                </div>
                <div class="modal-body">
                    <div id="error-message" class="alert alert-error">
                        An error occurred
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Logger utility -->
    <script src="./js/utils/Logger.js"></script>

    <!-- Core services -->
    <script src="./js/core/ProxyManager.js"></script>

    <!-- Main application -->
    <script src="./js/app.js"></script>

    <style>
        /* Loading Screen Styles */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #FF0000, #00FF00, #0000FF);
            background-size: 400% 400%;
            animation: gradientShift 3s ease infinite;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .loading-container {
            text-align: center;
            background-color: #FFFFFF;
            border: 4px solid #000000;
            box-shadow: 8px 8px 0 #000000;
            padding: 40px;
            max-width: 400px;
            width: 90%;
        }

        .loading-logo {
            font-family: 'Courier New', monospace;
            font-size: 32px;
            font-weight: bold;
            color: #FF0000;
            text-shadow: 3px 3px 0 #000000;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .loading-subtitle {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #0000FF;
            text-transform: uppercase;
            margin-bottom: 30px;
            font-weight: bold;
        }

        .loading-bar {
            width: 100%;
            height: 20px;
            border: 3px solid #000000;
            background-color: #FFFFFF;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }

        .loading-progress {
            height: 100%;
            background-color: #00FF00;
            width: 0%;
            animation: loadingAnimation 3s ease-in-out;
            border-right: 3px solid #000000;
        }

        @keyframes loadingAnimation {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        .loading-text {
            font-family: 'Courier New', monospace;
            font-size: 10px;
            color: #000000;
            text-transform: uppercase;
            font-weight: bold;
            animation: blink 1s infinite;
        }

        /* Navigation Styles */
        .nav-header {
            background-color: #000000;
            color: #FFFFFF;
            padding: 16px 0;
            border-bottom: 4px solid #FF0000;
        }

        .nav-brand {
            display: inline-block;
        }

        .nav-logo {
            display: inline;
            font-size: 24px;
            margin: 0;
            margin-right: 16px;
        }

        .nav-tagline {
            font-size: 12px;
            color: #00FF00;
            text-transform: uppercase;
        }

        .nav-controls {
            float: right;
        }

        .nav-controls .btn {
            margin-left: 8px;
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background-color: #FFFFFF;
            border: 4px solid #000000;
            box-shadow: 8px 8px 0 #000000;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            background-color: #000000;
            color: #FFFFFF;
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 18px;
        }

        .modal-body {
            padding: 24px;
        }

        .help-section {
            margin-bottom: 24px;
        }

        .help-section h3 {
            color: #FF0000;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .help-section ul {
            list-style: none;
            padding-left: 0;
        }

        .help-section li {
            margin-bottom: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .help-section li:before {
            content: "▶ ";
            color: #0000FF;
            font-weight: bold;
        }

        /* App Container */
        .app-container {
            min-height: 100vh;
            background-color: #FFFFFF;
        }

        .main-content {
            min-height: calc(100vh - 80px);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-controls {
                float: none;
                margin-top: 16px;
            }

            .nav-controls .btn {
                display: block;
                width: 100%;
                margin: 4px 0;
            }

            .loading-container {
                padding: 20px;
            }

            .loading-logo {
                font-size: 24px;
            }

            .modal-content {
                width: 95%;
                margin: 16px;
            }
        }
    </style>
</body>
</html>
