{"name": "geoshiftpro", "version": "1.0.0", "description": "A Neo-Brutalist VPN/Proxy bypass tool for local use with desktop and web applications", "main": "src/desktop/main.js", "homepage": "./", "author": "HectorTa1989", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/GeoShiftPro.git"}, "keywords": ["vpn", "proxy", "bypass", "neo-brutalism", "electron", "networking", "privacy", "security"], "scripts": {"start": "electron src/desktop/main.js", "dev": "node start-dev.js both", "dev:web": "node scripts/copy-files.js && webpack serve --config webpack.dev.js", "dev:electron": "node start-dev.js desktop", "dev:standalone": "electron src/desktop/main.js --dev", "build": "npm run build:web && npm run build:desktop", "build:web": "node scripts/copy-files.js && webpack --config webpack.prod.js", "build:desktop": "electron-builder", "build:desktop:win": "electron-builder --win", "build:desktop:mac": "electron-builder --mac", "build:desktop:linux": "electron-builder --linux", "copy-files": "node scripts/copy-files.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/ --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src/ --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,css,scss,json}\"", "deploy:netlify": "npm run build:web && netlify deploy --prod --dir=dist", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"axios": "^1.6.0", "core-js": "^3.33.0", "electron-store": "^8.1.0", "express": "^4.18.2", "node-fetch": "^3.3.2", "ws": "^8.14.2", "geoip-lite": "^1.4.7", "public-ip": "^6.0.1", "socks-proxy-agent": "^8.0.2", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.2"}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4", "electron-reload": "^2.0.0-alpha.1", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "html-webpack-plugin": "^5.5.3", "mini-css-extract-plugin": "^2.7.6", "css-minimizer-webpack-plugin": "^5.0.1", "terser-webpack-plugin": "^5.3.9", "css-loader": "^6.8.1", "sass-loader": "^13.3.2", "sass": "^1.69.5", "style-loader": "^3.3.3", "file-loader": "^6.2.0", "url-loader": "^4.1.1", "babel-loader": "^9.1.3", "@babel/core": "^7.23.2", "@babel/preset-env": "^7.23.2", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "jest": "^29.7.0", "eslint": "^8.52.0", "prettier": "^3.0.3", "concurrently": "^8.2.2", "wait-on": "^7.2.0", "netlify-cli": "^17.0.0"}, "build": {"appId": "com.hectortapia.geoshiftpro", "productName": "GeoShift Pro", "directories": {"output": "dist-desktop"}, "files": ["src/desktop/**/*", "src/core/**/*", "src/utils/**/*", "dist/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{js,jsx}", "!src/**/*.test.{js,jsx}", "!src/desktop/main.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}