#!/usr/bin/env node

/**
 * Development startup script for GeoShift Pro
 * Handles both web and desktop development with proper error handling
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔥 GeoShift Pro Development Startup');
console.log('===================================\n');

// Check if dependencies are installed
if (!fs.existsSync('node_modules')) {
  console.log('📦 Installing dependencies first...');
  exec('npm install', (error, stdout, stderr) => {
    if (error) {
      console.error('❌ Failed to install dependencies:', error);
      process.exit(1);
    }
    console.log('✅ Dependencies installed');
    startDevelopment();
  });
} else {
  startDevelopment();
}

function startDevelopment() {
  const args = process.argv.slice(2);
  const mode = args[0] || 'both';

  switch (mode) {
    case 'web':
      startWebDev();
      break;
    case 'electron':
    case 'desktop':
      startElectronDev();
      break;
    case 'both':
    default:
      startBothDev();
      break;
  }
}

function startWebDev() {
  console.log('🌐 Starting web development server...');
  
  // Copy files first
  exec('node scripts/copy-files.js', (error) => {
    if (error) {
      console.warn('⚠️  File copying failed, continuing anyway...');
    }
    
    // Start webpack dev server
    const webServer = spawn('npm', ['run', 'dev:web'], {
      stdio: 'inherit',
      shell: true
    });

    webServer.on('error', (error) => {
      console.error('❌ Web server failed to start:', error);
    });

    webServer.on('close', (code) => {
      console.log(`Web server exited with code ${code}`);
    });
  });
}

function startElectronDev() {
  console.log('🖥️  Starting Electron development...');
  
  // Start electron directly with standalone version
  const electronProcess = spawn('npx', ['electron', 'src/desktop/main.js', '--dev'], {
    stdio: 'inherit',
    shell: true
  });

  electronProcess.on('error', (error) => {
    console.error('❌ Electron failed to start:', error);
  });

  electronProcess.on('close', (code) => {
    console.log(`Electron exited with code ${code}`);
  });
}

function startBothDev() {
  console.log('🚀 Starting both web and desktop development...');
  
  // Copy files first
  exec('node scripts/copy-files.js', (error) => {
    if (error) {
      console.warn('⚠️  File copying failed, continuing anyway...');
    }
    
    // Start web server
    console.log('🌐 Starting web server...');
    const webServer = spawn('npm', ['run', 'dev:web'], {
      stdio: ['ignore', 'pipe', 'pipe'],
      shell: true
    });

    webServer.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('webpack compiled') || output.includes('Local:')) {
        console.log('✅ Web server is ready');
        
        // Start Electron after web server is ready
        setTimeout(() => {
          console.log('🖥️  Starting Electron...');
          const electronProcess = spawn('npx', ['electron', 'src/desktop/main.js', '--dev'], {
            stdio: 'inherit',
            shell: true
          });

          electronProcess.on('error', (error) => {
            console.error('❌ Electron failed to start:', error);
          });
        }, 2000);
      }
      process.stdout.write(`[WEB] ${output}`);
    });

    webServer.stderr.on('data', (data) => {
      process.stderr.write(`[WEB ERROR] ${data}`);
    });

    webServer.on('error', (error) => {
      console.error('❌ Web server failed to start:', error);
      // Start Electron with standalone version anyway
      startElectronDev();
    });

    // Handle process termination
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down development servers...');
      webServer.kill();
      process.exit(0);
    });
  });
}

// Show usage if help is requested
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
Usage: node start-dev.js [mode]

Modes:
  web      - Start only web development server
  desktop  - Start only Electron desktop app
  both     - Start both web and desktop (default)

Examples:
  node start-dev.js web
  node start-dev.js desktop
  node start-dev.js both
  node start-dev.js

The script will automatically:
- Install dependencies if needed
- Copy necessary files
- Start development servers
- Handle errors gracefully
`);
  process.exit(0);
}
