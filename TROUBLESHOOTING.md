# 🔧 GeoShift Pro Troubleshooting Guide

## Common Issues and Solutions

### 🚫 App Stuck at "INITIALIZING..."

**Problem**: The application loads but gets stuck at the initialization screen.

**Solutions**:

1. **Quick Fix - Use Standalone Version**:
   ```bash
   npm run dev:standalone
   ```
   This bypasses network issues and loads a working version immediately.

2. **Web Development Issues**:
   ```bash
   # Try the new development script
   node start-dev.js web
   
   # Or manually
   npm run copy-files
   npm run dev:web
   ```

3. **Desktop App Issues**:
   ```bash
   # Use the improved desktop script
   node start-dev.js desktop
   
   # Or directly
   npm run dev:standalone
   ```

### 🌐 Network/CORS Issues

**Problem**: Proxy loading fails due to CORS or network restrictions.

**Solutions**:
- The app now includes fallback demo proxies
- Initialization will continue even if external proxy loading fails
- Check browser console for detailed error messages

### 📦 Dependency Issues

**Problem**: Missing dependencies or build errors.

**Solutions**:
```bash
# Clean install
rm -rf node_modules package-lock.json
npm install

# Or use the installation script
node install.js
```

### ⚡ Development Server Issues

**Problem**: Webpack dev server won't start or crashes.

**Solutions**:
```bash
# Check if port 3000 is available
netstat -an | grep 3000

# Use different port
PORT=3001 npm run dev:web

# Or use standalone version
npm run dev:standalone
```

## 🚀 Recommended Development Workflow

### Option 1: Quick Start (Recommended)
```bash
# Install and start everything
node install.js
node start-dev.js
```

### Option 2: Web Development Only
```bash
node start-dev.js web
# Open http://localhost:3000
```

### Option 3: Desktop Development Only
```bash
node start-dev.js desktop
# Electron app opens automatically
```

### Option 4: Standalone (Always Works)
```bash
npm run dev:standalone
# Opens Electron with built-in HTML
```

## 🔍 Debugging Steps

### 1. Check Console Output
- Web: Open browser DevTools (F12)
- Desktop: DevTools open automatically in development

### 2. Verify File Structure
```
src/
├── web/
│   ├── standalone.html  ← Fallback version
│   ├── index.html       ← Main web version
│   └── js/
└── desktop/
    └── main.js          ← Electron entry point
```

### 3. Test Components Individually
```bash
# Test proxy manager
node -e "const PM = require('./src/web/js/core/ProxyManager.js'); new PM();"

# Test file copying
npm run copy-files

# Test webpack build
npm run build:web
```

## 🛠️ Environment-Specific Issues

### Windows
- Use PowerShell or Command Prompt as Administrator
- Ensure Windows Defender isn't blocking Electron

### macOS
- Allow Electron in Security & Privacy settings
- Use Terminal for all commands

### Linux
- Install required dependencies: `sudo apt-get install libnss3-dev libatk-bridge2.0-dev libdrm2 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2`

## 📊 Performance Optimization

### Reduce Loading Time
1. Use standalone version for development
2. Disable unnecessary features in development
3. Use local proxy lists instead of external APIs

### Memory Usage
- Close DevTools when not needed
- Limit proxy list size (already set to 20)
- Clear browser cache regularly

## 🔧 Configuration Options

### Environment Variables
Create `.env` file:
```
NODE_ENV=development
PORT=3000
ELECTRON_DEV=true
PROXY_TIMEOUT=5000
```

### Development Flags
```bash
# Skip proxy loading
SKIP_PROXY_LOADING=true npm run dev

# Use demo data only
USE_DEMO_DATA=true npm run dev

# Enable verbose logging
DEBUG=true npm run dev
```

## 📞 Getting Help

### Before Reporting Issues
1. Try the standalone version: `npm run dev:standalone`
2. Check this troubleshooting guide
3. Clear browser cache and restart
4. Update Node.js to latest LTS version

### Reporting Bugs
Include:
- Operating system and version
- Node.js version (`node --version`)
- Complete error messages
- Steps to reproduce
- Whether standalone version works

### Quick Diagnostics
```bash
# System info
node --version
npm --version
npx electron --version

# Project status
npm run copy-files
ls -la dist/

# Test basic functionality
node -e "console.log('Node.js is working')"
```

## ✅ Success Indicators

### Web App Working
- Browser opens to http://localhost:3000
- No console errors
- UI loads within 10 seconds
- "GEOSHIFT PRO" title visible

### Desktop App Working
- Electron window opens
- DevTools available (F12)
- No crash dialogs
- UI responsive

### Both Working
- Web server starts on port 3000
- Electron connects to web server
- Hot reloading works
- Changes reflect immediately

---

**Remember**: The standalone version (`npm run dev:standalone`) always works and provides full functionality for development and testing!
