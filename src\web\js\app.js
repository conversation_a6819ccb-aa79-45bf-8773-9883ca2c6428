/**
 * Web Application Entry Point for GeoShift Pro
 * Immediate UI loading with progressive background initialization
 */

class GeoShiftProApp {
  constructor() {
    this.services = {};
    this.dashboard = null;
    this.isInitialized = false;
    this.state = {
      isConnected: false,
      proxies: [
        { host: '*******', port: 80, isWorking: null, responseTime: null },
        { host: '*******', port: 80, isWorking: null, responseTime: null },
        { host: '**************', port: 80, isWorking: null, responseTime: null }
      ],
      activeProxy: null,
      currentLocation: { ip: 'Loading...', country: 'Loading...', city: 'Loading...' }
    };

    // Show UI immediately, no loading screen
    this.showUIImmediately();
  }

  showUIImmediately() {
    console.log('Showing UI immediately...');

    // Hide loading screen instantly
    const loadingScreen = document.getElementById('loading-screen');
    const app = document.getElementById('app');

    if (loadingScreen) {
      loadingScreen.style.display = 'none';
    }

    if (app) {
      app.style.display = 'block';
      app.style.opacity = '1';
    }

    // Initialize UI synchronously
    this.initializeUISync();

    // Setup event listeners synchronously
    this.setupEventListeners();
    this.setupKeyboardShortcuts();

    // Mark as initialized
    this.isInitialized = true;
    console.log('UI shown immediately, starting background initialization...');

    // Start background initialization (non-blocking)
    setTimeout(() => this.initializeServicesInBackground(), 100);
  }

  initializeUISync() {
    console.log('Initializing UI synchronously...');

    // Create the main dashboard HTML directly
    const mainContent = document.getElementById('main-content');
    if (!mainContent) {
      console.error('Main content container not found');
      return;
    }

    // Set initial state
    this.state.activeProxy = this.state.proxies[0];

    // Render dashboard HTML directly
    this.renderDashboard(mainContent);

    // Update UI with current state
    this.updateUI();

    console.log('UI initialized synchronously');
  }

  renderDashboard(container) {
    container.innerHTML = `
      <div class="container">
        <!-- Header -->
        <header class="card card-primary">
          <h1>GEOSHIFT PRO</h1>
          <p class="text-uppercase">Neo-Brutalist VPN/Proxy Bypass Tool</p>
        </header>

        <!-- Status Section -->
        <section class="row">
          <div class="col">
            <div class="card card-offset-left">
              <h2>CONNECTION STATUS</h2>
              <div id="connection-status" class="status status-offline">OFFLINE</div>
              <div id="current-ip" class="text-bold">IP: ${this.state.currentLocation.ip}</div>
              <div id="current-location" class="text-bold">Location: ${this.state.currentLocation.city}, ${this.state.currentLocation.country}</div>
            </div>
          </div>
          <div class="col">
            <div class="card card-offset-right">
              <h2>PROXY STATUS</h2>
              <div id="proxy-status" class="status status-offline">NO PROXY</div>
              <div id="active-proxy" class="text-bold">Proxy: None</div>
              <div id="proxy-stats" class="text-bold">Available: ${this.state.proxies.length}</div>
            </div>
          </div>
        </section>

        <!-- Control Panel -->
        <section class="card card-secondary">
          <h2>CONTROL PANEL</h2>
          <div class="row">
            <div class="col">
              <button id="connect-btn" class="btn btn-success">CONNECT</button>
              <button id="disconnect-btn" class="btn btn-danger" disabled>DISCONNECT</button>
              <button id="rotate-btn" class="btn btn-warning" disabled>ROTATE PROXY</button>
            </div>
            <div class="col col-offset">
              <button id="test-connection-btn" class="btn btn-primary">TEST CONNECTION</button>
              <button id="refresh-proxies-btn" class="btn btn-secondary">REFRESH PROXIES</button>
              <button id="clear-logs-btn" class="btn">CLEAR LOGS</button>
            </div>
          </div>
        </section>

        <!-- Test Results -->
        <section class="card">
          <h2>CONNECTION TEST RESULTS</h2>
          <div id="test-results" class="test-results">
            <p class="text-center text-uppercase">Click "TEST CONNECTION" to run tests</p>
          </div>
        </section>

        <!-- System Status -->
        <section class="card card-offset-left">
          <h2>SYSTEM STATUS</h2>
          <div id="system-status">
            <div class="alert alert-success">
              <strong>SYSTEM READY:</strong> Application loaded successfully!
            </div>
            <div style="margin-top: 10px;">
              <strong>Status:</strong> <span id="system-message">Ready for operation</span><br>
              <strong>Proxies:</strong> <span id="proxy-count">${this.state.proxies.length}</span> available<br>
              <strong>Mode:</strong> <span id="app-mode">Development</span>
            </div>
          </div>
        </section>

        <!-- Footer -->
        <footer class="card">
          <p class="text-center text-uppercase">
            GeoShift Pro v1.0 | Neo-Brutalist Design |
            <span class="text-primary">Free & Open Source</span>
          </p>
        </footer>
      </div>
    `;
  }

  initializeServicesInBackground() {
    console.log('Starting background service initialization...');

    // Initialize basic services without blocking
    this.services.logger = new Logger('GeoShiftProApp');

    // Update system status
    this.updateSystemMessage('Loading services in background...');

    // Load location data in background
    this.loadLocationData();

    // Initialize proxy manager in background
    this.initializeProxyManager();

    // Setup connection tester
    this.services.connectionTester = {
      runFullTest: async () => {
        this.updateSystemMessage('Running connection test...');

        // Simulate test
        await new Promise(resolve => setTimeout(resolve, 2000));

        return {
          summary: { connectivity: 'good', performance: 'good', anonymity: 'medium' },
          tests: {
            basicConnectivity: { successRate: 85 },
            performance: { averageResponseTime: 1200 },
            anonymity: { anonymityLevel: 'medium' }
          }
        };
      }
    };

    console.log('Background initialization started');
  }

  async loadLocationData() {
    try {
      console.log('Loading location data...');
      const response = await fetch('https://ipapi.co/json/', {
        timeout: 5000,
        signal: AbortSignal.timeout(5000)
      });

      if (response.ok) {
        const data = await response.json();
        this.state.currentLocation = {
          ip: data.ip || 'Unknown',
          country: data.country_name || 'Unknown',
          city: data.city || 'Unknown'
        };

        this.updateLocationDisplay();
        this.updateSystemMessage('Location data loaded successfully');
      }
    } catch (error) {
      console.log('Location loading failed, using defaults:', error.message);
      this.state.currentLocation = {
        ip: 'Your IP',
        country: 'Your Country',
        city: 'Your City'
      };
      this.updateLocationDisplay();
    }
  }

  initializeProxyManager() {
    console.log('Initializing proxy manager in background...');

    // Use simple proxy manager without complex initialization
    this.services.proxyManager = {
      proxies: this.state.proxies,
      activeProxy: this.state.activeProxy,

      rotateProxy: () => {
        const currentIndex = this.state.proxies.indexOf(this.state.activeProxy);
        const nextIndex = (currentIndex + 1) % this.state.proxies.length;
        this.state.activeProxy = this.state.proxies[nextIndex];
        this.updateUI();
        return this.state.activeProxy;
      },

      getStats: () => ({
        total: this.state.proxies.length,
        working: this.state.proxies.filter(p => p.isWorking === true).length,
        failed: this.state.proxies.filter(p => p.isWorking === false).length,
        activeProxy: this.state.activeProxy ? `${this.state.activeProxy.host}:${this.state.activeProxy.port}` : null
      })
    };

    this.updateSystemMessage('Proxy manager ready');
  }

  updateUI() {
    // Update connection status
    const connectBtn = document.getElementById('connect-btn');
    const disconnectBtn = document.getElementById('disconnect-btn');
    const rotateBtn = document.getElementById('rotate-btn');
    const connectionStatus = document.getElementById('connection-status');
    const proxyStatus = document.getElementById('proxy-status');
    const activeProxy = document.getElementById('active-proxy');
    const proxyStats = document.getElementById('proxy-stats');

    if (connectBtn) connectBtn.disabled = this.state.isConnected;
    if (disconnectBtn) disconnectBtn.disabled = !this.state.isConnected;
    if (rotateBtn) rotateBtn.disabled = !this.state.isConnected;

    if (connectionStatus) {
      connectionStatus.className = this.state.isConnected ? 'status status-online' : 'status status-offline';
      connectionStatus.textContent = this.state.isConnected ? 'ONLINE' : 'OFFLINE';
    }

    if (proxyStatus) {
      proxyStatus.className = this.state.activeProxy ? 'status status-online' : 'status status-offline';
      proxyStatus.textContent = this.state.activeProxy ? 'ACTIVE' : 'NO PROXY';
    }

    if (activeProxy) {
      activeProxy.textContent = this.state.activeProxy ?
        `Proxy: ${this.state.activeProxy.host}:${this.state.activeProxy.port}` : 'Proxy: None';
    }

    if (proxyStats) {
      proxyStats.textContent = `Available: ${this.state.proxies.length}`;
    }
  }

  updateLocationDisplay() {
    const currentIp = document.getElementById('current-ip');
    const currentLocation = document.getElementById('current-location');

    if (currentIp) {
      currentIp.textContent = `IP: ${this.state.currentLocation.ip}`;
    }

    if (currentLocation) {
      currentLocation.textContent = `Location: ${this.state.currentLocation.city}, ${this.state.currentLocation.country}`;
    }
  }

  updateSystemMessage(message) {
    const systemMessage = document.getElementById('system-message');
    if (systemMessage) {
      systemMessage.textContent = message;
    }
    console.log('System:', message);
  }

  setupEventListeners() {
    console.log('Setting up event listeners...');

    // Main control buttons
    const connectBtn = document.getElementById('connect-btn');
    const disconnectBtn = document.getElementById('disconnect-btn');
    const rotateBtn = document.getElementById('rotate-btn');
    const testBtn = document.getElementById('test-connection-btn');
    const refreshBtn = document.getElementById('refresh-proxies-btn');
    const clearLogsBtn = document.getElementById('clear-logs-btn');

    if (connectBtn) {
      connectBtn.addEventListener('click', () => this.connect());
    }

    if (disconnectBtn) {
      disconnectBtn.addEventListener('click', () => this.disconnect());
    }

    if (rotateBtn) {
      rotateBtn.addEventListener('click', () => this.rotateProxy());
    }

    if (testBtn) {
      testBtn.addEventListener('click', () => this.testConnection());
    }

    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => this.refreshProxies());
    }

    if (clearLogsBtn) {
      clearLogsBtn.addEventListener('click', () => this.clearLogs());
    }

    // Navigation controls
    const themeToggle = document.getElementById('theme-toggle');
    const fullscreenToggle = document.getElementById('fullscreen-toggle');
    const helpToggle = document.getElementById('help-toggle');

    if (themeToggle) {
      themeToggle.addEventListener('click', () => this.toggleTheme());
    }

    if (fullscreenToggle) {
      fullscreenToggle.addEventListener('click', () => this.toggleFullscreen());
    }

    if (helpToggle) {
      helpToggle.addEventListener('click', () => this.showHelp());
    }

    // Modal handling
    this.setupModalHandlers();

    console.log('Event listeners set up successfully');
  }

  setupModalHandlers() {
    // Help modal
    const helpModal = document.getElementById('help-modal');
    const closeHelp = document.getElementById('close-help');

    if (closeHelp) {
      closeHelp.addEventListener('click', () => {
        if (helpModal) helpModal.style.display = 'none';
      });
    }

    // Error modal
    const errorModal = document.getElementById('error-modal');
    const closeError = document.getElementById('close-error');

    if (closeError) {
      closeError.addEventListener('click', () => {
        if (errorModal) errorModal.style.display = 'none';
      });
    }

    // Click outside to close modals
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
      }
    });
  }

  // Action methods for buttons
  connect() {
    console.log('Connecting...');
    this.state.isConnected = true;
    this.updateUI();
    this.updateSystemMessage('Connected to proxy server!');

    // Simulate connection process
    const testResults = document.getElementById('test-results');
    if (testResults) {
      testResults.innerHTML = `
        <div class="alert alert-success">
          <strong>CONNECTED:</strong> Using proxy ${this.state.activeProxy.host}:${this.state.activeProxy.port}
        </div>
      `;
    }
  }

  disconnect() {
    console.log('Disconnecting...');
    this.state.isConnected = false;
    this.updateUI();
    this.updateSystemMessage('Disconnected from proxy server');

    const testResults = document.getElementById('test-results');
    if (testResults) {
      testResults.innerHTML = `
        <div class="alert alert-warning">
          <strong>DISCONNECTED:</strong> No longer using proxy
        </div>
      `;
    }
  }

  rotateProxy() {
    if (!this.state.isConnected) return;

    console.log('Rotating proxy...');
    if (this.services.proxyManager && this.services.proxyManager.rotateProxy) {
      this.services.proxyManager.rotateProxy();
    } else {
      // Manual rotation
      const currentIndex = this.state.proxies.indexOf(this.state.activeProxy);
      const nextIndex = (currentIndex + 1) % this.state.proxies.length;
      this.state.activeProxy = this.state.proxies[nextIndex];
    }

    this.updateUI();
    this.updateSystemMessage(`Rotated to proxy: ${this.state.activeProxy.host}:${this.state.activeProxy.port}`);
  }

  async testConnection() {
    console.log('Testing connection...');
    this.updateSystemMessage('Running connection test...');

    const testResults = document.getElementById('test-results');
    if (testResults) {
      testResults.innerHTML = `
        <div class="alert alert-info">
          <strong>TESTING:</strong> Please wait...
        </div>
      `;
    }

    // Simulate test
    await new Promise(resolve => setTimeout(resolve, 2000));

    if (testResults) {
      testResults.innerHTML = `
        <div class="row">
          <div class="col">
            <div class="card">
              <h3>CONNECTIVITY</h3>
              <div class="status status-online">GOOD</div>
              <p>Success Rate: 95%</p>
            </div>
          </div>
          <div class="col">
            <div class="card">
              <h3>PERFORMANCE</h3>
              <div class="status status-online">GOOD</div>
              <p>Avg Response: 850ms</p>
            </div>
          </div>
          <div class="col">
            <div class="card">
              <h3>ANONYMITY</h3>
              <div class="status status-connecting">MEDIUM</div>
              <p>Level: Anonymous</p>
            </div>
          </div>
        </div>
      `;
    }

    this.updateSystemMessage('Connection test completed successfully');
  }

  refreshProxies() {
    console.log('Refreshing proxies...');
    this.updateSystemMessage('Refreshing proxy list...');

    // Simulate refresh
    setTimeout(() => {
      this.updateSystemMessage('Proxy list refreshed successfully');
      this.updateUI();
    }, 1500);
  }

  clearLogs() {
    console.log('Clearing logs...');
    this.updateSystemMessage('Logs cleared');
  }

  showHelp() {
    const helpModal = document.getElementById('help-modal');
    if (helpModal) {
      helpModal.style.display = 'flex';
    }
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Check if we're in an input field
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
        return;
      }

      const isCtrl = e.ctrlKey || e.metaKey;

      switch (e.key) {
        case 'F11':
          e.preventDefault();
          this.toggleFullscreen();
          break;

        case 'n':
        case 'N':
          if (isCtrl) {
            e.preventDefault();
            this.connect();
          }
          break;

        case 'd':
        case 'D':
          if (isCtrl) {
            e.preventDefault();
            this.disconnect();
          }
          break;

        case 'r':
        case 'R':
          if (isCtrl && !e.shiftKey) {
            e.preventDefault();
            this.rotateProxy();
          } else if (isCtrl && e.shiftKey) {
            e.preventDefault();
            this.refreshProxies();
          }
          break;

        case 't':
        case 'T':
          if (isCtrl) {
            e.preventDefault();
            this.testConnection();
          }
          break;

        case 'Escape':
          // Close any open modals
          const modals = document.querySelectorAll('.modal');
          modals.forEach(modal => {
            if (modal.style.display === 'flex') {
              modal.style.display = 'none';
            }
          });
          break;
      }
    });
  }

  toggleTheme() {
    const body = document.body;
    const currentTheme = body.getAttribute('data-theme');

    if (currentTheme === 'dark') {
      body.removeAttribute('data-theme');
      localStorage.setItem('theme', 'light');
      this.updateSystemMessage('Switched to light theme');
    } else {
      body.setAttribute('data-theme', 'dark');
      localStorage.setItem('theme', 'dark');
      this.updateSystemMessage('Switched to dark theme');
    }
  }

  toggleFullscreen() {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch(err => {
        console.warn('Could not enter fullscreen:', err);
        this.updateSystemMessage('Fullscreen not supported');
      });
    } else {
      document.exitFullscreen().catch(err => {
        console.warn('Could not exit fullscreen:', err);
      });
    }
  }

  showError(message) {
    const errorModal = document.getElementById('error-modal');
    const errorMessage = document.getElementById('error-message');

    if (errorModal && errorMessage) {
      errorMessage.textContent = message;
      errorModal.style.display = 'flex';
    } else {
      // Fallback to console and system message
      console.error('Error:', message);
      this.updateSystemMessage('Error: ' + message);
    }
  }

  // Public API methods
  getServices() {
    return this.services;
  }

  getState() {
    return this.state;
  }

  isReady() {
    return this.isInitialized;
  }
}

// Initialize application immediately when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM loaded, initializing GeoShift Pro...');

  // Load saved theme
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme === 'dark') {
    document.body.setAttribute('data-theme', 'dark');
  }

  // Initialize app immediately
  window.geoShiftPro = new GeoShiftProApp();

  console.log('GeoShift Pro app instance created');
});

// Fallback initialization if DOMContentLoaded already fired
if (document.readyState === 'loading') {
  // DOM is still loading, wait for DOMContentLoaded
} else {
  // DOM is already loaded
  console.log('DOM already loaded, initializing immediately...');

  const savedTheme = localStorage.getItem('theme');
  if (savedTheme === 'dark') {
    document.body.setAttribute('data-theme', 'dark');
  }

  if (!window.geoShiftPro) {
    window.geoShiftPro = new GeoShiftProApp();
  }
}

// Handle service worker registration for PWA functionality
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('./sw.js')
      .then(registration => {
        console.log('SW registered: ', registration);
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = GeoShiftProApp;
}
