/**
 * Web Application Entry Point for GeoShift Pro
 * Initializes the application and manages global state
 */

class GeoShiftProApp {
  constructor() {
    this.services = {};
    this.dashboard = null;
    this.isInitialized = false;
    this.loadingTexts = [
      'INITIALIZING...',
      'LOADING PROXY ENGINES...',
      'CONNECTING TO SERVICES...',
      'PREPARING INTERFACE...',
      'READY TO LAUNCH!'
    ];
    this.currentLoadingIndex = 0;
    
    this.init();
  }

  async init() {
    try {
      console.log('Starting GeoShift Pro initialization...');

      // Show loading screen
      this.showLoadingScreen();

      // Initialize services with timeout
      console.log('Initializing services...');
      await Promise.race([
        this.initializeServices(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Service initialization timeout')), 12000)
        )
      ]);

      // Initialize UI
      console.log('Initializing UI...');
      await this.initializeUI();

      // Setup event listeners
      console.log('Setting up event listeners...');
      this.setupEventListeners();

      // Setup keyboard shortcuts
      console.log('Setting up keyboard shortcuts...');
      this.setupKeyboardShortcuts();

      // Hide loading screen and show app
      console.log('Showing application...');
      this.hideLoadingScreen();

      this.isInitialized = true;
      console.log('GeoShift Pro initialized successfully');

    } catch (error) {
      console.error('Failed to initialize GeoShift Pro:', error);

      // Try to show the app anyway with limited functionality
      console.log('Attempting to show app with limited functionality...');
      this.hideLoadingScreen();

      // Initialize basic UI even if services failed
      try {
        await this.initializeUI();
        this.setupEventListeners();
        this.setupKeyboardShortcuts();
        this.isInitialized = true;
        console.log('App loaded with limited functionality');
      } catch (uiError) {
        console.error('UI initialization also failed:', uiError);
        this.showError('Critical initialization failure. Please refresh the page.');
      }
    }
  }

  showLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    const loadingText = document.querySelector('.loading-text');

    // Animate loading text
    const textInterval = setInterval(() => {
      this.currentLoadingIndex = (this.currentLoadingIndex + 1) % this.loadingTexts.length;
      if (loadingText) {
        loadingText.textContent = this.loadingTexts[this.currentLoadingIndex];
      }
    }, 800);

    // Store interval for cleanup
    this.loadingTextInterval = textInterval;

    // Force hide loading screen after maximum time
    this.maxLoadingTimeout = setTimeout(() => {
      console.log('Force hiding loading screen due to timeout');
      this.hideLoadingScreen();
    }, 15000); // 15 seconds maximum
  }

  hideLoadingScreen() {
    // Clear loading text interval
    if (this.loadingTextInterval) {
      clearInterval(this.loadingTextInterval);
    }

    // Clear max loading timeout
    if (this.maxLoadingTimeout) {
      clearTimeout(this.maxLoadingTimeout);
    }

    // Hide loading screen with animation
    const loadingScreen = document.getElementById('loading-screen');
    const app = document.getElementById('app');

    if (loadingScreen && app) {
      loadingScreen.style.opacity = '0';
      loadingScreen.style.transition = 'opacity 0.5s ease-out';

      setTimeout(() => {
        loadingScreen.style.display = 'none';
        app.style.display = 'block';
        app.style.opacity = '0';
        app.style.transition = 'opacity 0.5s ease-in';

        setTimeout(() => {
          app.style.opacity = '1';
        }, 50);
      }, 500);
    }
  }

  async initializeServices() {
    // Initialize Logger
    this.services.logger = new Logger('GeoShiftProApp');

    // Initialize Proxy Manager
    this.services.proxyManager = new ProxyManager({
      maxRetries: 3,
      timeout: 10000,
      rotationInterval: 30000,
      healthCheckInterval: 60000
    });

    // Initialize simplified services for browser
    this.services.geoService = {
      getCurrentLocation: async () => {
        try {
          const response = await fetch('https://ipapi.co/json/');
          const data = await response.json();
          return {
            ip: data.ip,
            country: data.country_name,
            countryCode: data.country_code,
            region: data.region,
            city: data.city,
            latitude: parseFloat(data.latitude),
            longitude: parseFloat(data.longitude),
            timezone: data.timezone,
            isp: data.org
          };
        } catch (error) {
          console.error('Failed to get location:', error);
          return { ip: 'Unknown', country: 'Unknown', city: 'Unknown' };
        }
      }
    };

    this.services.connectionTester = {
      runFullTest: async () => {
        return {
          summary: { connectivity: 'good', performance: 'good', anonymity: 'medium' },
          tests: {
            basicConnectivity: { successRate: 85 },
            performance: { averageResponseTime: 1200 },
            anonymity: { anonymityLevel: 'medium' }
          }
        };
      }
    };

    // Wait for proxy manager to initialize with better error handling
    await new Promise((resolve, reject) => {
      let resolved = false;

      this.services.proxyManager.once('proxiesLoaded', () => {
        if (!resolved) {
          resolved = true;
          console.log('Proxy manager loaded successfully');
          resolve();
        }
      });

      this.services.proxyManager.once('error', (error) => {
        console.warn('Proxy manager error (continuing anyway):', error);
        if (!resolved) {
          resolved = true;
          resolve(); // Continue even with errors
        }
      });

      // Shorter timeout and continue anyway
      setTimeout(() => {
        if (!resolved) {
          resolved = true;
          console.log('Proxy initialization timeout, continuing with demo data');
          resolve();
        }
      }, 10000); // 10 seconds instead of 30
    });
  }

  async initializeUI() {
    const mainContent = document.getElementById('main-content');
    if (!mainContent) {
      throw new Error('Main content container not found');
    }

    // Initialize Dashboard
    this.dashboard = new Dashboard(mainContent, this.services);
    
    // Make dashboard globally available for debugging
    window.dashboard = this.dashboard;
  }

  setupEventListeners() {
    // Theme toggle
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
      themeToggle.addEventListener('click', () => this.toggleTheme());
    }

    // Fullscreen toggle
    const fullscreenToggle = document.getElementById('fullscreen-toggle');
    if (fullscreenToggle) {
      fullscreenToggle.addEventListener('click', () => this.toggleFullscreen());
    }

    // Help modal
    const helpToggle = document.getElementById('help-toggle');
    const helpModal = document.getElementById('help-modal');
    const closeHelp = document.getElementById('close-help');
    
    if (helpToggle && helpModal && closeHelp) {
      helpToggle.addEventListener('click', () => {
        helpModal.style.display = 'flex';
      });
      
      closeHelp.addEventListener('click', () => {
        helpModal.style.display = 'none';
      });
      
      helpModal.addEventListener('click', (e) => {
        if (e.target === helpModal) {
          helpModal.style.display = 'none';
        }
      });
    }

    // Error modal
    const errorModal = document.getElementById('error-modal');
    const closeError = document.getElementById('close-error');
    
    if (errorModal && closeError) {
      closeError.addEventListener('click', () => {
        errorModal.style.display = 'none';
      });
      
      errorModal.addEventListener('click', (e) => {
        if (e.target === errorModal) {
          errorModal.style.display = 'none';
        }
      });
    }

    // Window events
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });

    window.addEventListener('resize', () => {
      this.handleResize();
    });

    // Handle visibility change (tab switching)
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.handleTabHidden();
      } else {
        this.handleTabVisible();
      }
    });
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Check if we're in an input field
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
        return;
      }

      const isCtrl = e.ctrlKey || e.metaKey;
      
      switch (e.key) {
        case 'F11':
          e.preventDefault();
          this.toggleFullscreen();
          break;
          
        case 'n':
        case 'N':
          if (isCtrl) {
            e.preventDefault();
            this.dashboard?.connect();
          }
          break;
          
        case 'd':
        case 'D':
          if (isCtrl) {
            e.preventDefault();
            this.dashboard?.disconnect();
          }
          break;
          
        case 'r':
        case 'R':
          if (isCtrl && !e.shiftKey) {
            e.preventDefault();
            this.dashboard?.rotateProxy();
          } else if (isCtrl && e.shiftKey) {
            e.preventDefault();
            this.dashboard?.refreshProxies();
          }
          break;
          
        case 't':
        case 'T':
          if (isCtrl) {
            e.preventDefault();
            this.dashboard?.testConnection();
          }
          break;
          
        case 'Escape':
          // Close any open modals
          const modals = document.querySelectorAll('.modal');
          modals.forEach(modal => {
            if (modal.style.display === 'flex') {
              modal.style.display = 'none';
            }
          });
          break;
      }
    });
  }

  toggleTheme() {
    const body = document.body;
    const currentTheme = body.getAttribute('data-theme');
    
    if (currentTheme === 'dark') {
      body.removeAttribute('data-theme');
      localStorage.setItem('theme', 'light');
    } else {
      body.setAttribute('data-theme', 'dark');
      localStorage.setItem('theme', 'dark');
    }
  }

  toggleFullscreen() {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch(err => {
        console.warn('Could not enter fullscreen:', err);
      });
    } else {
      document.exitFullscreen().catch(err => {
        console.warn('Could not exit fullscreen:', err);
      });
    }
  }

  showError(message) {
    const errorModal = document.getElementById('error-modal');
    const errorMessage = document.getElementById('error-message');
    
    if (errorModal && errorMessage) {
      errorMessage.textContent = message;
      errorModal.style.display = 'flex';
    } else {
      // Fallback to alert
      alert('Error: ' + message);
    }
  }

  handleResize() {
    // Handle responsive layout changes
    if (this.dashboard) {
      // Trigger dashboard resize handling if needed
      this.dashboard.handleResize?.();
    }
  }

  handleTabHidden() {
    // Pause non-essential operations when tab is hidden
    if (this.services.proxyManager) {
      // Reduce update frequency
      this.services.proxyManager.config.healthCheckInterval = 120000; // 2 minutes
    }
  }

  handleTabVisible() {
    // Resume normal operations when tab becomes visible
    if (this.services.proxyManager) {
      // Restore normal update frequency
      this.services.proxyManager.config.healthCheckInterval = 60000; // 1 minute
    }
  }

  cleanup() {
    // Clean up resources before page unload
    if (this.services.proxyManager) {
      this.services.proxyManager.stop();
    }
    
    if (this.loadingTextInterval) {
      clearInterval(this.loadingTextInterval);
    }
  }

  // Public API methods
  getServices() {
    return this.services;
  }

  getDashboard() {
    return this.dashboard;
  }

  isReady() {
    return this.isInitialized;
  }
}

// Initialize application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Load saved theme
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme === 'dark') {
    document.body.setAttribute('data-theme', 'dark');
  }

  // Initialize app
  window.geoShiftPro = new GeoShiftProApp();
});

// Handle service worker registration for PWA functionality
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('./sw.js')
      .then(registration => {
        console.log('SW registered: ', registration);
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = GeoShiftProApp;
}
