<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GeoShift Pro - Neo-Brutalist VPN/Proxy Tool</title>
    
    <style>
        /* Inline Neo-Brutalist styles for standalone version */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Courier New', 'Monaco', 'Menlo', 'Consolas', monospace;
            font-size: 14px;
            line-height: 1.4;
            color: #000000;
            background-color: #FFFFFF;
        }

        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #FF0000, #00FF00, #0000FF);
            background-size: 400% 400%;
            animation: gradientShift 3s ease infinite;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .loading-container {
            text-align: center;
            background-color: #FFFFFF;
            border: 4px solid #000000;
            box-shadow: 8px 8px 0 #000000;
            padding: 40px;
            max-width: 400px;
            width: 90%;
        }

        .loading-logo {
            font-size: 32px;
            font-weight: bold;
            color: #FF0000;
            text-shadow: 3px 3px 0 #000000;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .loading-subtitle {
            font-size: 12px;
            color: #0000FF;
            text-transform: uppercase;
            margin-bottom: 30px;
            font-weight: bold;
        }

        .loading-text {
            font-size: 10px;
            color: #000000;
            text-transform: uppercase;
            font-weight: bold;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .app-container {
            min-height: 100vh;
            background-color: #FFFFFF;
            padding: 20px;
        }

        h1 {
            font-size: 32px;
            color: #FF0000;
            text-shadow: 4px 4px 0 #000000;
            margin-bottom: 20px;
            text-transform: uppercase;
        }

        .card {
            border: 4px solid #000000;
            background-color: #FFFFFF;
            box-shadow: 4px 4px 0 #000000;
            margin: 16px 0;
            padding: 24px;
        }

        .btn {
            display: inline-block;
            padding: 8px 16px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
            text-decoration: none;
            border: 4px solid #000000;
            background-color: #FFFFFF;
            color: #000000;
            cursor: pointer;
            box-shadow: 4px 4px 0 #000000;
            margin: 4px;
        }

        .btn:hover {
            transform: translate(2px, 2px);
            box-shadow: 2px 2px 0 #000000;
        }

        .btn-primary {
            background-color: #FF0000;
            color: #FFFFFF;
        }

        .status {
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            border: 3px solid #000000;
            margin: 4px;
        }

        .status-offline {
            background-color: #FF0000;
            color: #FFFFFF;
        }

        .status-online {
            background-color: #00FF00;
            color: #000000;
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-container">
            <div class="loading-logo">GEOSHIFT PRO</div>
            <div class="loading-subtitle">Neo-Brutalist VPN/Proxy Tool</div>
            <div id="loading-text" class="loading-text">INITIALIZING...</div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="app" class="app-container" style="display: none;">
        <h1>GEOSHIFT PRO</h1>
        
        <div class="card">
            <h2 style="color: #0000FF; margin-bottom: 10px;">CONNECTION STATUS</h2>
            <div id="connection-status" class="status status-offline">OFFLINE</div>
            <div style="margin-top: 10px;">
                <strong>IP:</strong> <span id="current-ip">Loading...</span><br>
                <strong>Location:</strong> <span id="current-location">Loading...</span>
            </div>
        </div>

        <div class="card">
            <h2 style="color: #0000FF; margin-bottom: 10px;">CONTROLS</h2>
            <button id="connect-btn" class="btn btn-primary">CONNECT</button>
            <button id="test-btn" class="btn">TEST CONNECTION</button>
            <button id="refresh-btn" class="btn">REFRESH PROXIES</button>
        </div>

        <div class="card">
            <h2 style="color: #0000FF; margin-bottom: 10px;">SYSTEM STATUS</h2>
            <div id="status-message">Application loaded successfully!</div>
            <div style="margin-top: 10px;">
                <strong>Proxies Available:</strong> <span id="proxy-count">0</span><br>
                <strong>Active Proxy:</strong> <span id="active-proxy">None</span>
            </div>
        </div>
    </div>

    <script>
        // Simple standalone application
        class StandaloneGeoShiftPro {
            constructor() {
                this.isConnected = false;
                this.proxies = [];
                this.init();
            }

            async init() {
                console.log('Initializing Standalone GeoShift Pro...');
                
                // Simulate loading
                const loadingTexts = [
                    'INITIALIZING...',
                    'LOADING COMPONENTS...',
                    'SETTING UP INTERFACE...',
                    'READY!'
                ];
                
                let index = 0;
                const loadingText = document.getElementById('loading-text');
                
                const interval = setInterval(() => {
                    if (index < loadingTexts.length) {
                        loadingText.textContent = loadingTexts[index];
                        index++;
                    } else {
                        clearInterval(interval);
                        this.showApp();
                    }
                }, 1000);

                // Initialize demo data
                this.proxies = [
                    { host: '*******', port: 80 },
                    { host: '*******', port: 80 },
                    { host: '**************', port: 80 }
                ];

                // Setup event listeners
                setTimeout(() => {
                    this.setupEventListeners();
                    this.updateUI();
                }, 4000);
            }

            showApp() {
                const loadingScreen = document.getElementById('loading-screen');
                const app = document.getElementById('app');
                
                loadingScreen.style.opacity = '0';
                loadingScreen.style.transition = 'opacity 0.5s ease-out';
                
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    app.style.display = 'block';
                }, 500);
            }

            setupEventListeners() {
                document.getElementById('connect-btn').addEventListener('click', () => {
                    this.toggleConnection();
                });

                document.getElementById('test-btn').addEventListener('click', () => {
                    this.testConnection();
                });

                document.getElementById('refresh-btn').addEventListener('click', () => {
                    this.refreshProxies();
                });
            }

            toggleConnection() {
                this.isConnected = !this.isConnected;
                this.updateUI();
                
                const statusEl = document.getElementById('status-message');
                if (this.isConnected) {
                    statusEl.textContent = 'Connected via proxy server!';
                } else {
                    statusEl.textContent = 'Disconnected from proxy server.';
                }
            }

            testConnection() {
                const statusEl = document.getElementById('status-message');
                statusEl.textContent = 'Testing connection... Please wait.';
                
                setTimeout(() => {
                    statusEl.textContent = 'Connection test completed. Status: Working!';
                }, 2000);
            }

            refreshProxies() {
                const statusEl = document.getElementById('status-message');
                statusEl.textContent = 'Refreshing proxy list...';
                
                setTimeout(() => {
                    statusEl.textContent = 'Proxy list refreshed successfully!';
                    this.updateUI();
                }, 1500);
            }

            updateUI() {
                const connectBtn = document.getElementById('connect-btn');
                const statusEl = document.getElementById('connection-status');
                const proxyCountEl = document.getElementById('proxy-count');
                const activeProxyEl = document.getElementById('active-proxy');
                const currentIpEl = document.getElementById('current-ip');
                const currentLocationEl = document.getElementById('current-location');

                if (this.isConnected) {
                    connectBtn.textContent = 'DISCONNECT';
                    statusEl.className = 'status status-online';
                    statusEl.textContent = 'ONLINE';
                    activeProxyEl.textContent = `${this.proxies[0].host}:${this.proxies[0].port}`;
                    currentIpEl.textContent = this.proxies[0].host;
                    currentLocationEl.textContent = 'Proxy Location';
                } else {
                    connectBtn.textContent = 'CONNECT';
                    statusEl.className = 'status status-offline';
                    statusEl.textContent = 'OFFLINE';
                    activeProxyEl.textContent = 'None';
                    currentIpEl.textContent = 'Your Real IP';
                    currentLocationEl.textContent = 'Your Real Location';
                }

                proxyCountEl.textContent = this.proxies.length;
            }
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            window.geoShiftPro = new StandaloneGeoShiftPro();
        });
    </script>
</body>
</html>
