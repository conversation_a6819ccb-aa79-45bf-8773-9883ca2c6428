/**
 * Browser-compatible ProxyManager
 * Simplified version for web application
 */

class ProxyManager {
  constructor(config = {}) {
    this.config = {
      maxRetries: 3,
      timeout: 10000,
      rotationInterval: 30000,
      healthCheckInterval: 60000,
      ...config
    };
    
    this.proxies = [];
    this.activeProxy = null;
    this.proxyIndex = 0;
    this.healthCheckTimer = null;
    this.rotationTimer = null;
    this.eventListeners = {};
    
    this.init();
  }

  // Simple event emitter
  on(event, callback) {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = [];
    }
    this.eventListeners[event].push(callback);
  }

  once(event, callback) {
    const onceCallback = (...args) => {
      callback(...args);
      this.off(event, onceCallback);
    };
    this.on(event, onceCallback);
  }

  off(event, callback) {
    if (this.eventListeners[event]) {
      this.eventListeners[event] = this.eventListeners[event].filter(cb => cb !== callback);
    }
  }

  emit(event, ...args) {
    if (this.eventListeners[event]) {
      this.eventListeners[event].forEach(callback => callback(...args));
    }
  }

  async init() {
    try {
      console.log('Initializing ProxyManager...');
      await this.loadFreeProxies();
      this.startHealthCheck();
      this.startRotation();
      console.log('ProxyManager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize ProxyManager:', error);
      // Don't throw error, just emit it and continue with demo data
      this.emit('error', error);

      // Ensure we have some proxies even if loading failed
      if (this.proxies.length === 0) {
        console.log('Creating fallback proxy data...');
        this.proxies = [
          { host: '*******', port: 80, protocol: 'http', isWorking: null, lastChecked: null, responseTime: null, failures: 0 },
          { host: '*******', port: 80, protocol: 'http', isWorking: null, lastChecked: null, responseTime: null, failures: 0 }
        ];
        this.activeProxy = this.proxies[0];
        this.emit('proxiesLoaded', this.proxies.length);
      }
    }
  }

  async loadFreeProxies() {
    console.log('Starting proxy loading...');

    // Use demo proxies for development/testing
    const demoProxies = [
      '*******:80',
      '*******:80',
      '**************:80',
      '*******:80',
      '***********:80',
      '*********:80',
      '**********:80',
      '**************:80',
      '*************:80',
      '*************:80'
    ];

    const allProxies = [];

    // Try to load from external sources first
    const sources = [
      'https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=5000&country=all&ssl=all&anonymity=all&format=textplain'
    ];

    let loadedFromExternal = false;

    for (const source of sources) {
      try {
        console.log(`Attempting to load from: ${source}`);
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        const response = await fetch(source, {
          signal: controller.signal,
          mode: 'cors',
          headers: {
            'User-Agent': 'GeoShiftPro/1.0'
          }
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const text = await response.text();
          const proxies = this.parseProxyList(text);
          if (proxies.length > 0) {
            allProxies.push(...proxies);
            loadedFromExternal = true;
            console.log(`Loaded ${proxies.length} proxies from ${source}`);
          }
        }
      } catch (error) {
        console.warn(`Failed to load proxies from ${source}:`, error.message);
      }
    }

    // If external loading failed, use demo proxies
    if (!loadedFromExternal) {
      console.log('External proxy loading failed, using demo proxies');
      allProxies.push(...demoProxies);
    }

    // Remove duplicates and validate
    this.proxies = [...new Set(allProxies)]
      .map(proxy => this.parseProxy(proxy))
      .filter(proxy => proxy !== null)
      .slice(0, 20); // Limit to 20 proxies for performance

    console.log(`Total unique proxies loaded: ${this.proxies.length}`);

    if (this.proxies.length === 0) {
      // Fallback to basic demo proxies
      this.proxies = demoProxies.slice(0, 5).map(proxy => this.parseProxy(proxy)).filter(p => p);
      console.log('Using fallback demo proxies:', this.proxies.length);
    }

    this.activeProxy = this.proxies[0];
    console.log('Proxy loading completed, emitting proxiesLoaded event');
    this.emit('proxiesLoaded', this.proxies.length);
  }

  parseProxyList(data) {
    const lines = data.split('\n').filter(line => line.trim());
    return lines.map(line => line.trim()).filter(line => {
      return /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d{1,5}$/.test(line);
    });
  }

  parseProxy(proxyString) {
    try {
      const [host, port] = proxyString.split(':');
      if (!host || !port) return null;
      
      return {
        host: host.trim(),
        port: parseInt(port.trim()),
        protocol: 'http',
        isWorking: null,
        lastChecked: null,
        responseTime: null,
        failures: 0
      };
    } catch (error) {
      return null;
    }
  }

  getCurrentProxy() {
    return this.activeProxy;
  }

  rotateProxy() {
    if (this.proxies.length === 0) return null;
    
    this.proxyIndex = (this.proxyIndex + 1) % this.proxies.length;
    this.activeProxy = this.proxies[this.proxyIndex];
    
    console.log(`Rotated to proxy: ${this.activeProxy.host}:${this.activeProxy.port}`);
    this.emit('proxyRotated', this.activeProxy);
    
    return this.activeProxy;
  }

  async testProxy(proxy, testUrl = 'https://httpbin.org/ip') {
    const startTime = Date.now();
    
    try {
      // Note: Direct proxy testing from browser is limited due to CORS
      // This is a simplified version for demonstration
      const response = await fetch(testUrl, {
        method: 'GET',
        headers: {
          'User-Agent': 'GeoShiftPro/1.0'
        }
      });

      const responseTime = Date.now() - startTime;
      
      if (response.ok) {
        proxy.isWorking = true;
        proxy.responseTime = responseTime;
        proxy.failures = 0;
        proxy.lastChecked = new Date();
        return true;
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      proxy.isWorking = false;
      proxy.failures += 1;
      proxy.lastChecked = new Date();
      console.debug(`Proxy test failed for ${proxy.host}:${proxy.port} - ${error.message}`);
      return false;
    }
  }

  async performHealthCheck() {
    console.log('Starting proxy health check...');
    
    const testPromises = this.proxies.slice(0, 10).map(proxy => this.testProxy(proxy));
    await Promise.allSettled(testPromises);
    
    const workingProxies = this.proxies.filter(p => p.isWorking === true);
    const failedProxies = this.proxies.filter(p => p.isWorking === false);
    
    console.log(`Health check complete: ${workingProxies.length} working, ${failedProxies.length} failed`);
    
    // Remove proxies with too many failures
    this.proxies = this.proxies.filter(p => p.failures < 5);
    
    // If current proxy is not working, rotate
    if (this.activeProxy && !this.activeProxy.isWorking) {
      this.rotateProxy();
    }
    
    this.emit('healthCheckComplete', {
      working: workingProxies.length,
      failed: failedProxies.length,
      total: this.proxies.length
    });
  }

  startHealthCheck() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }
    
    this.healthCheckTimer = setInterval(() => {
      this.performHealthCheck();
    }, this.config.healthCheckInterval);
  }

  startRotation() {
    if (this.rotationTimer) {
      clearInterval(this.rotationTimer);
    }
    
    this.rotationTimer = setInterval(() => {
      this.rotateProxy();
    }, this.config.rotationInterval);
  }

  stop() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }
    
    if (this.rotationTimer) {
      clearInterval(this.rotationTimer);
      this.rotationTimer = null;
    }
    
    console.log('ProxyManager stopped');
  }

  getStats() {
    const working = this.proxies.filter(p => p.isWorking === true).length;
    const failed = this.proxies.filter(p => p.isWorking === false).length;
    const untested = this.proxies.filter(p => p.isWorking === null).length;
    
    return {
      total: this.proxies.length,
      working,
      failed,
      untested,
      activeProxy: this.activeProxy ? `${this.activeProxy.host}:${this.activeProxy.port}` : null
    };
  }
}

// Export for browser
window.ProxyManager = ProxyManager;
